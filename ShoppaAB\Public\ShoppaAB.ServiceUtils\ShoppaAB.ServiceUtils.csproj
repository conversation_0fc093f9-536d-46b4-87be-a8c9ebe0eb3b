<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net471</TargetFramework>
		<Company>Shoppa AB</Company>
		<Authors>Shoppa AB</Authors>
		<Description>ShoppaAB Service Utilities Library</Description>
		<Copyright>Shoppa AB</Copyright>
		<GeneratePackageOnBuild>true</GeneratePackageOnBuild>
		<GenerateAssemblyInfo>false</GenerateAssemblyInfo>
		<NoWarn>CS1591,CS1573</NoWarn>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Core" />
		<PackageReference Include="Azure.Storage.Blobs" />
		<PackageReference Include="Azure.Storage.Common" />
		<PackageReference Include="GitInfo">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.ApplicationInsights" />
		<PackageReference Include="Microsoft.ApplicationInsights.Agent.Intercept" />
		<PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" />
		<PackageReference Include="Microsoft.ApplicationInsights.EventCounterCollector" />
		<PackageReference Include="Microsoft.ApplicationInsights.PerfCounterCollector" />
		<PackageReference Include="Microsoft.ApplicationInsights.WindowsServer" />
		<PackageReference Include="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" />
		<PackageReference Include="Microsoft.ApplicationInsights.WorkerService" />
		<PackageReference Include="Microsoft.Azure.KeyVault.Core" />
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" />
		<PackageReference Include="Microsoft.Extensions.Configuration" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" />
		<PackageReference Include="Microsoft.Extensions.Configuration.CommandLine" />
		<PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" />
		<PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.FileProviders.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.FileProviders.Physical" />
		<PackageReference Include="Microsoft.Extensions.FileSystemGlobbing" />
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.Logging" />
		<PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
		<PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" />
		<PackageReference Include="Microsoft.Extensions.Logging.Configuration" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" />
		<PackageReference Include="Microsoft.Extensions.Logging.EventLog" />
		<PackageReference Include="Microsoft.Extensions.Logging.EventSource" />
		<PackageReference Include="Microsoft.Extensions.Options" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" />
		<PackageReference Include="Microsoft.Extensions.Primitives" />
		<PackageReference Include="MySql.Data" />
		<PackageReference Include="Newtonsoft.Json" />
		<PackageReference Include="protobuf-net" />
		<PackageReference Include="ShoppaAB.Services.IO" />
		<PackageReference Include="ShoppaAB.ThinRpc" />
		<PackageReference Include="System.Buffers" />
		<PackageReference Include="System.Diagnostics.DiagnosticSource" />
		<PackageReference Include="System.IO" />
		<PackageReference Include="System.IO.Hashing" />
		<PackageReference Include="System.Memory" />
		<PackageReference Include="System.Memory.Data" />
		<PackageReference Include="System.Net.Http" />
		<PackageReference Include="System.Numerics.Vectors" />
		<PackageReference Include="System.Runtime" />
		<PackageReference Include="System.Runtime.CompilerServices.Unsafe" />
		<PackageReference Include="System.Runtime.InteropServices.RuntimeInformation" />
		<PackageReference Include="System.Security.Cryptography.Algorithms" />
		<PackageReference Include="System.Security.Cryptography.Encoding" />
		<PackageReference Include="System.Security.Cryptography.Primitives" />
		<PackageReference Include="System.Security.Cryptography.X509Certificates" />
		<PackageReference Include="System.Text.Encodings.Web" />
		<PackageReference Include="System.Text.Json" />
		<PackageReference Include="System.Threading.Tasks.Extensions" />
		<PackageReference Include="System.ValueTuple" />
		<PackageReference Include="WindowsAzure.Storage" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\ShoppaAB.Objects\ShoppaAB.Objects.csproj" />
		<ProjectReference Include="..\ShoppaAB.Remoting\ShoppaAB.Remoting.csproj" />
		<ProjectReference Include="..\ShoppaAB.Remoting.User\ShoppaAB.Remoting.User.csproj" />
		<ProjectReference Include="..\ShoppaAB.MySQL\ShoppaAB.MySQL.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Reference Include="System.Data" />
		<Reference Include="System.Xml" />
		<Reference Include="System.Configuration" />
		<Reference Include="System.ServiceProcess" />
		<Reference Include="System.Web" />
	</ItemGroup>

	<ItemGroup>
		<Compile Include="..\..\AssemblyInfoInc.cs">
			<Link>Properties\AssemblyInfoInc.cs</Link>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<None Include="app.config" />
		<None Include="ApplicationInsights.config" />
	</ItemGroup>

</Project>
