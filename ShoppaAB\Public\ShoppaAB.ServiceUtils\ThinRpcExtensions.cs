﻿// using Devart.Data.MySql; // Commented out - Devart packages not available in current package sources
using MySql.Data.MySqlClient; // Using standard MySQL.Data instead of Devart
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights.WorkerService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using ShoppaAB.Services.IO;
using ShoppaAB.Remoting.User;
using ShoppaAB.ThinRpc;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Reflection;
using ShoppaAB.ServiceUtils.ApplicationInsights;
using ShoppaAB.ThinRpc.Async;

namespace ShoppaAB.ServiceUtils
{
    public static class ThinRpcExtensions
    {
        internal static System.Threading.AsyncLocal<ThinRpcLogicalOperation> _taskCorrelation = new System.Threading.AsyncLocal<ThinRpcLogicalOperation>();


        //https://github.com/dotnet/runtime/blob/main/src/libraries/System.Diagnostics.DiagnosticSource/src/HierarchicalRequestId.md
        //http://blog.i3arnon.com/2015/05/16/logicaloperationstack-async-bug/

        public static IServiceCollection AddApplicationInsights(this IServiceCollection services, Action<ApplicationInsightsServiceOptions> options)
        {
            return services
                .AddSingleton<ITelemetryInitializer, ThinRpcTelemetryInitializer>()
                .AddApplicationInsightsTelemetryWorkerService(options);
        }

        private static IEnumerable<Type> FindThinRpcManagers(Assembly assembly)
        {
            foreach (Type type in assembly.GetTypes())
            {
                if (type.GetCustomAttributes(typeof(ThinRpcEndpoint), false).Length > 0)
                {
                    yield return type;
                }
            }
        }

        public static IServiceCollection AddThinRpcManagers(this IServiceCollection services)
        {
            return AddThinRpcManagers(services, Assembly.GetCallingAssembly());
        }

        public static IServiceCollection AddThinRpcManagers(this IServiceCollection services, Assembly assembly)
        {
            var managers = FindThinRpcManagers(assembly);
            var managerMap = new Dictionary<string, Type>();

            foreach (var manager in managers)
            {
                services.AddSingleton(manager);
                ThinRpcEndpoint attr = manager.GetCustomAttribute<ThinRpcEndpoint>();
                managerMap[attr.ObjectUri] = manager;
            }
            services.AddSingleton<IServiceResolver>(sp => new ServiceResolver(sp, new ReadOnlyDictionary<string, Type>(managerMap)));

            return services;
        }

        public static void InitializeThinRpcEndpoints(this IServiceProvider provider)
        {
            InitializeThinRpcEndpoints(provider, Assembly.GetCallingAssembly());
        }

        public static void InitializeThinRpcEndpoints(this IServiceProvider provider, Assembly assembly)
        {
            var managers = FindThinRpcManagers(assembly);

            foreach (var manager in managers)
            {
                _ = provider.GetRequiredService(manager);
            }
        }

        public static IServiceCollection AddAsyncThinRpcProxy<T>(this IServiceCollection serviceCollection, string url) where T : class
        {
            if (url == null) throw new ArgumentNullException($"Url not configured for ThinRpcProxy {typeof(T)}!");

            return serviceCollection
                .AddServiceFactory(() => ActivatorAsync.Get<T>(url));
        }

        public static IServiceCollection AddStorageManagers<TEnum>(this IServiceCollection serviceCollection, IConfigurationSection configurationSection) where TEnum : Enum
        {
            var namedServiceFactory = new NamedServiceFactory<IStorageManager, TEnum>(null);

            var managers = Enum.GetValues(typeof(TEnum));
            foreach (TEnum manager in managers)
            {
                var name = Enum.GetName(typeof(TEnum), manager);
                var section = configurationSection.GetSection(name) ?? throw new ArgumentNullException($"configurationSection.GetSection({name})");

                string connectionString = section.GetValue<string>("ConnectionString");
                string providerName = section.GetValue<string>("ProviderName");

                if (string.IsNullOrEmpty(connectionString))
                    throw new ArgumentNullException($"ConnectionString for storage manager: {manager} not correctly defined");
                if (string.IsNullOrEmpty(providerName))
                    throw new ArgumentNullException($"ProviderName for storage manager: {manager} not correctly defined");

                string trimmedConnectionString = connectionString.Trim(' ', '\t', '\n', '\r');
                string trimmedProviderName = providerName.Trim(' ', '\t', '\n', '\r');

                namedServiceFactory.Register(manager, _ =>
                {
                    return StorageManagerFactory.CreateStorageManager(
                        trimmedConnectionString,
                        trimmedProviderName);
                });
            }

            return serviceCollection.AddSingleton<INamedServiceFactory<IStorageManager, TEnum>>(namedServiceFactory);
        }

        public static IServiceCollection AddMySqlConnections<TEnum>(this IServiceCollection serviceCollection, IConfigurationSection configurationSection) where TEnum : Enum
        {
            var namedServiceFactory = new NamedServiceFactory<MySqlConnection, TEnum>(null);

            var keys = Enum.GetValues(typeof(TEnum));
            foreach (TEnum key in keys)
            {
                var name = Enum.GetName(typeof(TEnum), key);
                var connectionString = configurationSection.GetValue<string>(name) ?? throw new ArgumentNullException($"Connectionstring {name} not found in configuration.");
                var trimmedConnectionString = connectionString.Trim(' ', '\t', '\n', '\r');
                namedServiceFactory.Register(key, _ => new MySqlConnection(trimmedConnectionString));
            }

            return serviceCollection.AddSingleton<INamedServiceFactory<MySqlConnection, TEnum>>(namedServiceFactory);
        }

        public static void UseApplicationInsights(this ConnectionPool connectionPool, TelemetryClient telemetryClient)
        {
            connectionPool.InsightsCollector = new ApplicationInsightsCollector(telemetryClient);
            TrackDependencies(connectionPool, telemetryClient);
        }

        public static void UseApplicationInsights(this Listener listener, TelemetryClient telemetryClient)
        {
            TrackOperations(listener, telemetryClient);
        }

        private static void TrackOperations(Listener listener, TelemetryClient telemetryClient)
        {
            listener.RequestStarted += OnRequestStarted;
            listener.RequestCompleted += OnRequestCompleted;
            listener.RequestFailed += OnRequestFailed;

            void OnRequestStarted(object sender, RequestStartedEventArgs e)
            {
                if (e.Headers.TryGetValue("Request-Id", out var requestId))
                {
                    if (e.Headers.TryGetValue("Correlation-Context", out var correlationContext))
                    {
                        //see https://github.com/dotnet/runtime/blob/main/src/libraries/System.Diagnostics.DiagnosticSource/src/HttpCorrelationProtocol.md
                        //TODO: store these somewhere so we can propagate them to dependencies
                    }
                }
                else
                {
                    requestId = $"|{GenerateUniqueOperationId()}.";
                }

                _taskCorrelation.Value = new ThinRpcLogicalOperation(requestId, null);


                var operationName = $"{e.ServiceName}.{e.MethodName}({string.Join(", ", e.ParameterTypes)})";
                var request = telemetryClient.StartOperation<RequestTelemetry>(operationName);
                _taskCorrelation.Value.Request = request;
            }

            void OnRequestCompleted(object sender, RequestCompletedEventArgs e)
            {
                var request = _taskCorrelation.Value.Request;

                var requestTelemetry = request.Telemetry;
                requestTelemetry.ResponseCode = "200";
                requestTelemetry.Success = true;

                //Trace.WriteLine($"Request    {requestTelemetry.Id} = {requestTelemetry.ResponseCode}");
                telemetryClient.StopOperation(request);

                _taskCorrelation.Value = null;
            }

            void OnRequestFailed(object sender, RequestFailedEventArgs e)
            {
                var request = _taskCorrelation.Value.Request;
                var requestTelemetry = request.Telemetry;
                requestTelemetry.ResponseCode = GetResultCodeFrom(e.Exception);

                //We treat unauthenticated requests within the server layer as successful as far as insights are concerned
                if (requestTelemetry.ResponseCode == "401")
                {
                    requestTelemetry.Success = true;
                }
                else
                {
                    requestTelemetry.Success = false;
                    telemetryClient.TrackException(e.Exception);
                }

                if (requestTelemetry.ResponseCode.StartsWith("5"))
                {
                    //Only track exceptions if they are our own fault
                    telemetryClient.TrackException(e.Exception);
                }

                telemetryClient.StopOperation(request);
                _taskCorrelation.Value = null;
            }
        }

        private static string GetResultCodeFrom(Exception exception)
        {
            //https://en.wikipedia.org/wiki/List_of_HTTP_status_codes#5xx_server_errors
            //https://docs.microsoft.com/en-us/windows/win32/winsock/windows-sockets-error-codes-2

            const int WSAETIMEDOUT = 10060;
            const int WSAECONNREFUSED = 10061;
            const int WSAEHOSTDOWN = 10064;

            var innerException = exception.InnerException;

            switch (innerException)
            {
                case ExpiredTokenException _:
                case InvalidOperationException invalidOperation when invalidOperation.Message == "The validation GUID has expired.":
                    return "401";

                case System.Net.Sockets.SocketException socketException:
                    switch (socketException.ErrorCode)
                    {
                        case WSAETIMEDOUT:
                        case WSAECONNREFUSED:
                        case WSAEHOSTDOWN:
                            return "504";

                        default:
                            Trace.TraceInformation($"Found no explicit conversion for SocketException error code {socketException.ErrorCode}: {socketException.Message}");
                            break;
                    }
                    break;

                case null:
                    Trace.TraceInformation($"Found no explicit conversion for {exception.GetType()}: {exception.Message}");
                    break;

                default:
                    Trace.TraceInformation($"Found no explicit conversion for inner {innerException.GetType()}: {innerException.Message}");
                    break;
            }

            return "500";
        }

        private static void TrackDependencies(ConnectionPool connectionPool, TelemetryClient telemetryClient)
        {
            connectionPool.DependencyHeaders += (sender, e) =>
            {
                var dependencyTelemetry = new DependencyTelemetry
                {
                    Type = "ThinRpc",
                    Target = e.Target,
                    Name = e.DependencyName
                };
                var dependency = telemetryClient.StartOperation(dependencyTelemetry);

                if (_taskCorrelation.Value == null)
                    _taskCorrelation.Value = new ThinRpcLogicalOperation(null, null);
                _taskCorrelation.Value.Dependency = dependency;

                var requestId = CreateRequestId(dependencyTelemetry.Id);
                e.Headers = new[] {
                      $"Request-Id: {requestId}",
                    //$"Correlation-Context: {correlation key/value pairs here}", //See https://github.com/dotnet/runtime/blob/main/src/libraries/System.Diagnostics.DiagnosticSource/src/HttpCorrelationProtocol.md
                };

                //var operation = dependencyTelemetry.Context.Operation;
                //System.Diagnostics.Trace.WriteLine($"Dependency {dependencyTelemetry.Id} @ {operation.Id} ({operation.ParentId}) = [{requestId}] > {dependencyTelemetry.Target} {dependencyTelemetry.Data}");
            };

            connectionPool.DependencyException += (sender, e) =>
            {
                var dependency = _taskCorrelation.Value.Dependency;
                telemetryClient.TrackException(e.Exception);

                //var telemetry = dependency.Telemetry;
                //Trace.WriteLine($"Depfailure [{telemetry.Id}] = {e.Exception.Message}");
            };

            connectionPool.DependencyCompleted += (sender, e) =>
            {
                var dependency = _taskCorrelation.Value.Dependency;
                if (_taskCorrelation.Value.Request == null)
                    _taskCorrelation.Value = null;
                else
                    _taskCorrelation.Value.Dependency = null;

                if (dependency != null)
                {
                    var telemetry = dependency.Telemetry;
                    telemetry.ResultCode = e.Success ? "200" : "400";
                    telemetry.Success = e.Success;

                    telemetry.Metrics.Add("Bytes sent", e.BytesSent);
                    telemetry.Metrics.Add("Bytes received", e.BytesReceived);
                    telemetry.Metrics.Add("Time to first byte", e.TimeToFirstByte.TotalSeconds);

                    //Trace.WriteLine($"Dependency {dependency.Telemetry.Id} = {telemetry.ResultCode}");
                    telemetryClient.StopOperation(dependency);
                }
            };
        }

        private static string CreateRequestId(string dependencyId)
        {
            var parentRequestId = "|";

            //TODO: Replace all usages of _taskCorrelation with an Activity instance to conform with AppInsights default
            if (_taskCorrelation.Value?.Request != null)
            {
                var logicalOperation = _taskCorrelation.Value;
                parentRequestId = $"{logicalOperation.RequestId}{logicalOperation.Request.Telemetry.Id}.";
            }
            else if (Activity.Current?.RootId != null)
            {
                var activity = Activity.Current;

                parentRequestId = $"|{activity.RootId}.";
                if (activity.ParentId != null && activity.ParentId != "00000000")
                    parentRequestId += $"{activity.ParentSpanId}.";
            }

            return $"{parentRequestId}{dependencyId}.";
        }

        public static string GenerateUniqueOperationId()
        {
            return Convert.ToBase64String(Guid.NewGuid().ToByteArray())
                .Replace("/", "_")
                .Replace("+", "-")
                .Substring(0, 22);
        }
    }
}
